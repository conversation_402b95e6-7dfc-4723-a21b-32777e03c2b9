---
title: "STAT5002 Lab2 Worksheet"
subtitle: "Introduction to Statistics"
author: "University of Sydney STAT5002"
output:
  html_document:
    fig_caption: yes
    number_sections: yes
    self_contained: yes
    theme: flatly
    toc: true
    toc_depth: 3
    toc_float: true
    code_folding: show
---
<style>
h2 { /* Header 2 */
    font-size: 22px
}
</style>

# Import data and IDA


- Save the data-file `math1005_cleaned.csv`  in your data folder within your STAT5002 folder.

- **Import** the data into a variable called `math1005`:

```{r}
math1005 = read.csv("data/math1005_cleaned.csv")
```

This data was collected from an **optional survey** for MATH1005 students at the start of 2022 Semester 2. There are a total of **928 students** enrolled in this course. We will also use this data set next week in lectures. 

- How many survey **responses** are there? How many **variables** are there?

```{r}
str(math1005)

```

- What are the **columns** of your data and what are their **type**? Are they classified correctly?
    - *Hint: Use the function `str `. Other useful functions are `names` and`head`*.
     
```{r}
dim(math1005)

```


# Quantitative Data

## Height

- Create a **graphical summary** for the **height** distribution of the cohort. Comment on the distribution, are there any **outliers**? Are there any issues with the data?

```{r}

par(mfrow = c(1,2)) # 1 row with 2 column
hist(math1005$Height)
boxplot(math1005$Height)


```

**Answer**: The height distribution seems roughly symmetric with one outlier!

- Calculate some **numerical summaries** for the **heights**. Briefly summarise your findings. Here you may need to remove `NA`s in the variable before proceed.  

**Hint:** check `?na.omit` for removing `NA`s. 

```{r}

height = na.omit(math1005$Height)

mean(height)
sd(height). #sample sd

```


## Write a function in R to compute the population standard deviation

In the lectures, we learned how to write a function in R. Here is your turn, we want to write a function to compute the **population standard deviation** of a data set. Let's name the function `sdpop`. 

- Complete the function in the following.

```{r}
# complete the following function
sdpop <- function(X){
  m = mean(X)
  s = sqrt(sum((X - m)^2)/length(X))
  return(s)
  
}
```

```{r}
sdpop(height)

```


- Calculate the population SD of height and check if it is correct. 

```{r}
### Apply your function sdpop here to height and check its correctness

```

## Age

- Create a **graphical summary** for the **age** distribution of the cohort. Comment on the distribution, are there any **outliers**? Are there any issues with the data? 

```{r}
par(mfrow = c(1,2))
hist(math1005$Age)
boxplot(math1005$Age)

```


- Calculate some **numerical summaries** for the **ages**. Briefly summarise your findings.

```{r}

mean(math1005$Age)
median(math1005$Age)


```


## Shoe Size

- Create a **graphical summary** for the **shoe size** distribution of the cohort. Comment on the distribution, are there any **outliers**? Are there any issues with the data? **Hint:** Some might response using a different shoe size convention, perhaps using EU instead of US.

```{r}
par(mfrow = c(1,2))
hist(math1005$ShoeSize)
boxplot(math1005$ShoeSize)

```

- Perform any appropriate **data cleaning**. You may also need to remove `NA`s here. 

```{r}
math = na.omit(math1005$ShoeSize)

math1005$ShoeSize <- ifelse(math1005$ShoeSize >= 200 & math1005$ShoeSize <= 320,
                            math1005$ShoeSize / 6.35 - 33,
                            math1005$ShoeSize)

math1005$ShoeSize <- ifelse(math1005$ShoeSize > 20 & math1005$ShoeSize < 50,
                            math1005$ShoeSize - 33,
                            math1005$ShoeSize)

```


- Calculate some **numerical summaries** for the **shoe sizes**. Briefly summarise your findings.
  
```{r}
mean(math1005$ShoeSize)


```

# Qualitative Data

## International

- Create a **graphical summary** for the distribution of **domestic and international students** in the cohort. 
  - *Hint: what type of graphical summary would be the most appropriate? You can refer to code in previous lectures and labs*.

```{r}
par(mfrow = c(1,2))
barplot(table(math1005$International),
        main = "Domestic vs International",
        col = c("skyblue", "green"),
        ylab = "Count")
pie(table(math1005$International),
    main = "Domestic vs International",
    col = c("skyblue", "green"))


```

- What is the **proportion** of **international students**? Briefly summarise your findings.
  - *Hint: `math1005$International == "International"` creates a list of `TRUE` and `FALSE` of whether each student is international or not. R can interpret `TRUE` as 1 and `FALSE` as 0.*
```{r}
sum(math1005$International == "International") / length(math1005$International)
sum(math1005 == "International") / length(math1005$International)


```

## Gender

- Create a **graphical summary** for the **gender** distribution of the cohort.

```{r}
### write code here
barplot(table(math1005$Gender))
```


- What is the **proportion** of **females**? Briefly summarise your findings

```{r}
### write code here


```


## Country

This task contains some additional visualisation techniques not covered in lectures. These are not mandatory for learning other topics of this unit but can be useful in real life.  

- Create a **barplot** for the **Country** that the students in the cohort were born in. Is it meaningful? Are there any issues with the data? How could this be improved?

```{r}
table(math1005$Country)
barplot(table(math1005$Country))



```

- Make a barplot of the **top 10 countries** among the cohort. Briefly summarise your findings. 
  - *Hint: the `sort` function might be useful!*

```{r}
majors = table(math1005$Country)
majors = sort(majors, decreasing = TRUE)
majors = majors[1:10]

barplot(majors)
```


- **Extension**: make a **word cloud** of all the majors. Briefly summarise your findings.

You will need to first **install the** `wordcloud` **and** `tm` **packages**. To do this, you can run the first line without the `#`. Once installed, they will remain on your computer and you do not need to run this line ever again. But in each document you want to make a word cloud, you will need to include `library(wordcloud)` to **load the package**. 

```{r, warning = F, message = F}
#install.packages(c("wordcloud", "tm"))
#library(wordcloud)
#wordcloud(math1005$Country, min.freq = 1)
```




