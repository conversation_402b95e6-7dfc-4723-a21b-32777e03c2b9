from __future__ import annotations
import os, json, requests

def classify_sentiment(text: str) -> float:
    """Use an OpenAI-compatible API to classify sentiment.
    Returns a float in [-1, 1]. If anything fails, raises RuntimeError.
    The prompt asks the model to output ONLY a JSON like {"score": -0.42}.
    """
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    if not api_key:
        raise RuntimeError("OPENAI_API_KEY not set")
    url = f"{base_url}/chat/completions"
    prompt = (
        "You are a sentiment rater. Rate the user's message strictly as a float in [-1,1],\n"
        "where -1 is very negative, 0 neutral, +1 very positive.\n"
        "Only reply with a compact JSON: {\"score\": <float>}.\n"
    )
    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": text},
        ],
        "temperature": 0.0,
    }
    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
    resp = requests.post(url, headers=headers, data=json.dumps(payload), timeout=30)
    resp.raise_for_status()
    data = resp.json()
    content = data["choices"][0]["message"]["content"].strip()
    # Expect a small JSON
    try:
        obj = json.loads(content)
        score = float(obj.get("score", 0.0))
        return max(-1.0, min(1.0, score))
    except Exception as e:
        raise RuntimeError(f"Bad API response: {content}") from e
