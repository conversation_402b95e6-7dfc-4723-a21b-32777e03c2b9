from __future__ import annotations

NEGATIVE_WORDS = {
    "sad", "depressed", "anxious", "anxiety", "angry", "upset", "tired",
    "lonely", "worthless", "guilty", "shame", "overwhelmed", "stressed",
    "hate", "hopeless", "cry", "crying", "panic", "panicking"
}

def score(text: str) -> float:
    """Return a sentiment score in [-1, 1]. Negative values mean negative sentiment.
    Very simple: counts negative words and normalizes.
    """
    tokens = [t.strip(".,!?;:").lower() for t in text.split()]
    neg_hits = sum(t in NEGATIVE_WORDS for t in tokens)
    if not tokens:
        return 0.0
    raw = -neg_hits / max(5, len(tokens))  # clamp by minimal length
    return max(-1.0, min(1.0, raw))
