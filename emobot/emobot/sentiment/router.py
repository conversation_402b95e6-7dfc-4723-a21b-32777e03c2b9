from __future__ import annotations
import os
from typing import Optional
import numpy as np
from .rule_based import score as rb_score

def analyze(text: str) -> float:
    """Try API sentiment if configured; otherwise rule-based."""
    use_api = bool(os.getenv("OPENAI_API_KEY"))
    if use_api:
        try:
            from ..apis.openai_client import classify_sentiment
            return classify_sentiment(text)
        except Exception:
            # Fail gracefully to rule-based
            pass
    return rb_score(text)

class SentimentTracker:
    """Keeps a simple moving average of the last N scores using numpy."""
    def __init__(self, window: int = 5) -> None:
        self.window = window
        self._values: list[float] = []

    def update(self, s: float) -> float:
        self._values.append(float(s))
        if len(self._values) > self.window:
            self._values.pop(0)
        arr = np.array(self._values, dtype=float)
        return float(arr.mean())  # moving average
