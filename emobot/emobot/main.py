from __future__ import annotations
import sys, random
from .storage import log_line, load_notes, save_notes, load_responses
from .sentiment.router import analyze, SentimentTracker

BANNER = """\
=============================================
 EmoBot — a tiny emotional support chatbot
 Type 'help' for options, 'quit' to exit.
=============================================
"""

HELP = """\
Commands:
  help        Show this help
  note ...    Save a short note (kept locally in data/user_notes.json)
  show        Show your saved notes
  clear       Clear all saved notes
  quit        Exit
Just talk to me in natural language any time.
"""

def pick(lst: list[str]) -> str:
    return random.choice(lst) if lst else ""

def respond(user_text: str, responses: dict, tracker: SentimentTracker) -> str:
    s = analyze(user_text)
    avg = tracker.update(s)
    # More flow control: adapt based on current and moving-average sentiment
    out = []
    out.append(f"(debug) sentiment={s:+.2f}, moving_avg={avg:+.2f}")  # can comment out for release

    if s <= -0.4 or avg <= -0.4:
        out.append(pick(responses["ack"]))  # validate
        out.append(pick(responses["grounding"]))  # skill
    elif s < 0.0:
        out.append(pick(responses["ack"]))
        out.append(pick(responses["reframe"]))  # gentle reframe
    else:
        out.append("I’m glad you shared. How are you feeling right now on a 1–10 scale?")

    out.append(pick(responses["resources"]))  # resource tip
    return " " .join(out)

def main() -> None:
    print(BANNER)
    responses = load_responses()
    notes = load_notes()
    tracker = SentimentTracker(window=5)

    while True:
        try:
            user = input("you> ").strip()
        except (EOFError, KeyboardInterrupt):
            print("\nbye.")
            break

        if not user:
            continue

        if user.lower() in {"quit", "exit"}:
            print("Take care. You’re not alone.")
            break
        if user.lower() == "help": 
            print(HELP); continue
        if user.lower().startswith("note "):
            msg = user[5:].strip()
            notes.setdefault("notes", []).append(msg)
            save_notes(notes)
            print("Saved. You can type 'show' to view notes.")
            log_line(f"NOTE: {msg}")
            continue
        if user.lower() == "show":
            if not notes.get("notes"): print("(no notes yet)")
            else:
                for i, n in enumerate(notes["notes"], 1):
                    print(f"{i}. {n}")
            continue
        if user.lower() == "clear":
            notes = {"notes": []}; save_notes(notes)
            print("Notes cleared.")
            continue

        # normal chat path
        reply = respond(user, responses, tracker)
        print(f"bot> {reply}")
        log_line(f"USER: {user}")
        log_line(f"BOT: {reply}")

if __name__ == "__main__":
    main()
