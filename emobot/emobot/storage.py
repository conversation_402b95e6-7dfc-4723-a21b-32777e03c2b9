from __future__ import annotations
import json, os, datetime, pathlib
BASE = pathlib.Path(__file__).resolve().parents[1]

def log_line(text: str) -> None:
    logs = BASE / "logs"
    logs.mkdir(exist_ok=True, parents=True)
    fname = logs / f"chat_{datetime.date.today().isoformat()}.log"
    with fname.open("a", encoding="utf-8") as f:
        ts = datetime.datetime.now().strftime("%H:%M:%S")
        f.write(f"[{ts}] {text}\n")

def load_notes() -> dict:
    path = BASE / "data" / "user_notes.json"
    if not path.exists():
        return {"notes": []}
    return json.loads(path.read_text(encoding="utf-8"))

def save_notes(notes: dict) -> None:
    path = BASE / "data" / "user_notes.json"
    path.write_text(json.dumps(notes, indent=2), encoding="utf-8")

def load_responses() -> dict:
    path = BASE / "data" / "responses.json"
    return json.loads(path.read_text(encoding="utf-8"))
