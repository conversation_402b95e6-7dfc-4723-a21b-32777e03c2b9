# EmoBot — A Minimal Emotional Support Chatbot (Python-only)

This is a small **CLI** chatbot that supports users experiencing negative emotions.
It demonstrates the COMP9001 advanced topics:
- **File I/O (Week 9):** conversation logs in `logs/` and simple JSON "memory" at `data/user_notes.json`.
- **Testing (Week 10):** unit tests in `tests/` (`pytest` or `python -m unittest`).
- **Multi-dimensional lists/dicts (Week 12):** response templates in `data/responses.json` organized by categories; conversation state machine uses nested structures.
- **Numpy (Week 12):** tracks a moving average of sentiment scores to adapt responses across the session.

> Optional: You can enable an external API (e.g., OpenAI-compatible) by setting an API key in environment variables. If no API is set, it falls back to a rule-based analyzer.

## Quick start
```bash
# (recommended) create venv
python -m venv .venv && source .venv/bin/activate  # Windows: .venv\Scripts\activate

pip install -r requirements.txt

# Run chatbot
python -m emobot.main
```

### Use with an OpenAI-compatible API (optional)
Set the environment variables:
- `OPENAI_API_KEY` — your key
- `OPENAI_BASE_URL` — (optional) custom base url if you use a compatible provider

The bot will then use the API for sentiment classification; otherwise it uses a simple native rule-based model.

## Tests
```bash
# either with unittest
python -m unittest discover -s tests -v

# or with pytest (if installed)
pytest -q
```

## What to submit
Upload this whole folder (or the zip we generated) to your grader or LMS.
Files to include:
- `emobot/` package (source code)
- `data/responses.json`, `data/user_notes.json` (app creates/updates)
- `logs/` (runtime logs)
- `tests/`
- `requirements.txt`, `README.md`

## Notes
This is an educational demo and **not** a medical device. For emergencies, advise users to contact local crisis lines.
