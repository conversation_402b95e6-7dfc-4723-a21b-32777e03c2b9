import unittest
from emobot.sentiment.rule_based import score as rb_score
from emobot.sentiment.router import SentimentTracker

class TestSentiment(unittest.TestCase):
    def test_rule_based_negative(self):
        s = rb_score("I feel sad and anxious today")
        self.assertLess(s, 0.0)

    def test_tracker_moving_average(self):
        tr = SentimentTracker(window=3)
        vals = [-1.0, -0.5, 0.0]
        avgs = [tr.update(v) for v in vals]
        self.assertAlmostEqual(avgs[-1], sum(vals)/3, places=6)

if __name__ == '__main__':
    unittest.main()
