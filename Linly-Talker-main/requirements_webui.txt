# PyTorch and its dependencies
# These libraries include PyTorch and its related packages, supporting CUDA 11.8.
--extra-index-url https://download.pytorch.org/whl/torch_stable.html
torch
torchvision
torchaudio
# torch==2.4.1+cu118
# torchvision==0.19.1+cu118
# torchaudio==2.4.1+cu118

# Installation source for PyTorch: -f https://download.pytorch.org/whl/cu118
# Example installation command:

# pip install torch==2.4.1 torchvision==0.19.1 torchaudio==2.4.1 --index-url https://download.pytorch.org/whl/cu118
# pip install tb-nightly -i https://mirrors.aliyun.com/pypi/simple

# General tools and libraries
numba
tqdm
pyyaml
ffmpeg-python
gdown
requests
imageio[ffmpeg]
omegaconf
spaces
moviepy
librosa==0.10.2
ultralytics # for wav2lipv2
gradio==4.*
scikit_learn==1.4.2

# SadTalker related libraries
numpy==1.23.4
face_alignment==1.3.5
imageio==2.19.3
imageio-ffmpeg==0.4.7
resampy==0.3.1
pydub==0.25.1 
scipy==1.10.1
kornia==0.6.8
yacs==0.1.8
joblib==1.2.0
facexlib==0.3.0
scikit-image==0.19.3
protobuf==3.20.2
basicsr==1.4.2
gfpgan==1.3.8
matplotlib==3.7.5

# MuseTalk related libraries
diffusers==0.27.2
huggingface_hub==0.25.2
accelerate==0.28.0
opencv-python==********
soundfile==0.12.1
transformers==4.39.2
# pip install --no-cache-dir -U openmim
# mim install mmengine 
# mim install "mmcv>=2.0.1" 
# mim install "mmdet>=3.1.0" 
# mim install "mmpose>=1.1.0" 

# # PaddleTTS related libraries
# paddlepaddle==2.5.2
# paddlespeech==1.4.1
# opencc==1.1.1

# ASR (Automatic Speech Recognition) related libraries
openai
modelscope
funasr>=1.0.0
edge-tts>=6.1.18
openai-whisper
zhconv

# LLM (Large Language Model) related libraries
openai
g4f
curl_cffi
grpcio-status==1.48.2
google-generativeai
google-api-python-client==2.126.0
tiktoken
accelerate
einops 
transformers_stream_generator==0.0.4
sentencepiece

# GPT-SoVITS related libraries
# numba==0.56.4
# pytorch-lightning
# onnxruntime
# tqdm
# cn2an
# pypinyin
# pyopenjtalk
# g2p_en
# modelscope==1.10.0
# chardet
# PyYAML
# psutil
# jieba_fast
# jieba
# LangSegment


# CosyVoice  related libraries
# conformer==0.3.2
# lightning==2.2.4
# wget==3.2
# HyperPyYAML==1.2.2
# WeTextProcessing==1.0.3