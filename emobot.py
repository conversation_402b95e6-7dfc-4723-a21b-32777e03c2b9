# -*- coding: utf-8 -*-
"""
A tiny console chatbot for negative-emotion support (English only).
- Pure Python standard library (no external dependencies)
- Rule-based keyword matching with simple negation/intensity handling
- Crisis-first logic with AU resources (edit for your region if needed)
"""

import re
import random
import sys

# ---------- 1) Lexicons -------------------------------------------------------

NEGATIONS = {
    "not", "no", "dont", "don't", "cant", "can't", "isnt", "isn't", "never", "hardly", "barely", "without"
}
INTENSIFIERS = {
    "very", "so", "super", "really", "extremely", "quite", "too", "kinda", "sort of", "a bit", "a little"
}

CRISIS_TERMS = {
    # suicidal ideation / self-harm
    "suicide", "kill myself", "end my life", "self-harm", "self harm", "cut myself",
    "jump off", "dont want to live", "don't want to live", "i want to die", "i want die",
    "life has no meaning", "no reason to live"
}

CATS = {
    "sad": {
        "keywords": {
            "sad", "down", "depressed", "blue", "heartbroken", "upset", "hurt", "lonely",
            "numb", "empty", "overwhelmed"
        },
        "prompts": [
            "It sounds heavy. What part of this feels the most painful right now?",
            "I hear a lot of hurt in what you shared. Would you tell me what happened?",
            "Thank you for opening up. Is there one small thing that might help you feel 1% better?"
        ],
    },
    "anxious": {
        "keywords": {
            "anxious", "anxiety", "worried", "worry", "fear", "afraid", "panic", "panicky",
            "nervous", "on edge", "stressed", "pressure"
        },
        "prompts": [
            "Anxiety can be exhausting. What’s the number one worry on your mind?",
            "Let’s shrink it. What’s one doable step in the next 24 hours?",
            "When anxiety spikes, what has helped you slow it down before?"
        ],
    },
    "angry": {
        "keywords": {
            "angry", "mad", "furious", "pissed", "frustrated", "annoyed", "unfair", "resentful", "rage"
        },
        "prompts": [
            "It makes sense to feel angry. Which part crossed your boundaries the most?",
            "Your feelings point to what matters. What change would you want if you could ask for it?",
            "Catching the emotion is a strong first step. Where would you like to start?"
        ],
    },
    "tired": {
        "keywords": {
            "tired", "exhausted", "burned out", "burnt out", "fatigued", "drained", "worn out", "sleepy", "overworked"
        },
        "prompts": [
            "You’ve been pushing hard. What’s the kindest form of rest you can take next?",
            "Maybe we set a smaller target to recharge a little first. What could that be?",
            "When energy is low, basics help: food, water, a brief walk, or a short nap. Which sounds doable?"
        ],
    },
}

GENERIC_PROMPTS = [
    "I’m here and listening. Could you share a bit more so I can better understand?",
    "Thanks for telling me. What feels most stuck or painful right now?",
    "If we break this into smaller pieces, which piece would you like to tackle first?"
]

# ---------- 2) Utilities ------------------------------------------------------

def normalize(text: str) -> str:
    """Lowercase and collapse whitespace."""
    return re.sub(r"\s+", " ", text.strip().lower())

def contains_crisis(text: str) -> bool:
    t = normalize(text)
    return any(term in t for term in CRISIS_TERMS)

def score_hit(text: str, kw: str) -> float:
    """
    Very simple scoring for English:
    - **** if keyword is present as a substring (word boundary preferred)
    - +0.5 if an intensifier appears within 12 chars before the keyword
    - -0.8 if a negation appears within 12 chars before the keyword
    """
    t = normalize(text)
    score = 0.0
    idx = 0
    while True:
        m = re.search(rf"\b{re.escape(kw)}\b", t[idx:]) or re.search(re.escape(kw), t[idx:])
        if not m:
            break
        start = idx + m.start()
        score += 1.0
        window = t[max(0, start - 12):start]
        if any(n in window.split() for n in NEGATIONS):
            score -= 0.8
        if any(i in window for i in INTENSIFIERS):
            score += 0.5
        idx = idx + m.end()
    return score

def score_category(text: str, keywords: set) -> float:
    return sum(score_hit(text, kw) for kw in keywords)

def classify_emotion(text: str):
    best_cat, best_score = None, 0.0
    for cat, cfg in CATS.items():
        s = score_category(text, cfg["keywords"])
        if s > best_score:
            best_cat, best_score = cat, s
    return best_cat if best_score >= 1.0 else None

def reflect(text: str, limit: int = 60) -> str:
    """Return a short reflective snippet from user's message."""
    t = re.sub(r"\s+", " ", text.strip())
    return t if len(t) <= limit else (t[:limit] + "…")

# ---------- 3) Responses ------------------------------------------------------

def crisis_reply():
    # Keep language calm, validating, and action-oriented.
    return (
        "I’m really glad you told me—your safety matters.\n"
        "If you’re thinking about harming yourself or feel you can’t stay safe, please reach out **now**:\n"
        "• Australia — Lifeline 13 11 14 (24/7) or call **000** in an emergency.\n"
        "• If you’re outside Australia, contact your local emergency number or a crisis hotline.\n"
        "If you want, tell me what feels hardest right now. I’m here to listen."
    )

def empathy_reply(cat: str, user_text: str) -> str:
    cfg = CATS.get(cat)
    base = random.choice(cfg["prompts"]) if cfg else random.choice(GENERIC_PROMPTS)
    return f"I hear: “{reflect(user_text)}”. {base}"

def generic_reply(user_text: str) -> str:
    return f"I’m here with you. You said: “{reflect(user_text)}”. {random.choice(GENERIC_PROMPTS)}"

# ---------- 4) Orchestrator ---------------------------------------------------

def respond(user_text: str) -> str:
    if contains_crisis(user_text):
        return crisis_reply()
    cat = classify_emotion(user_text)
    if cat:
        return empathy_reply(cat, user_text)
    return generic_reply(user_text)

def main():
    print("CareBot (English-only, local rule-based). Type your message. Type 'quit' to exit.")
    while True:
        try:
            user = input("> ").strip()
        except (EOFError, KeyboardInterrupt):
            print("\nTake care. See you next time.")
            break
        if user.lower() in {"q", "quit", "exit"}:
            print("Take care. See you next time.")
            break
        if not user:
            print("(I’m here. Take your time.)")
            continue
        print(respond(user))

if __name__ == "__main__":
    # Ensure UTF-8 output on Windows terminals
    if sys.platform.startswith("win"):
        import io  # noqa
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
    main()
