print("Welcome to the University of Sydney Course Registration System!\n")

num_9001 = 2
num_9003 = 3
num_9017 = 2

class9001 = []
class9003 = []
class9017 = []

tut_group = 0


run = True
while run:
    command = []
    command = input("Enter command: ").split()
    
    action = command[0].lower()
    if action in ['register', 'drop', 'schedule'] and len(command) > 1:
        studentID = str(command[1])
        if len(studentID) != 9 or not studentID.isdigit():
            print("Invalid student ID. Must be 9 digits.")
            continue  # 跳过后续处理
        else:
            # 计算tutorial group的逻辑
            lasttwodigit = int(studentID[-2:])
            tut_group = ""
            if 00 <= lasttwodigit <= 24:
                tut_group = "1"
            elif 25 <= lasttwodigit <= 49:
                tut_group = "2"
            elif 50 <= lasttwodigit <= 74:
                tut_group = "3"
            elif 75 <= lasttwodigit <= 99:
                tut_group = "4"

    if action == 'register' and len(studentID) == 9:
        if len(command) < 3:
            print("Usage: REGISTER <student_id> <course>")
        elif len(studentID) == 9:
            if command[2].lower() == 'comp9001':
                alreadyin = any(sid == command[1] for sid, course, tut in class9001)
                if alreadyin:
                    print(f"Student {command[1]} is already registered in COMP9001.")
                else:
                    if len(class9001) < num_9001:
                        class9001.append((command[1], command[2], tut_group))
                        print(f"Student {command[1]} has been registered in {command[2].upper()} (Tutorial {tut_group}).")
                    elif len(class9001) >= 2:
                        print(f"Course COMP9001 is full. Student {command[1]} could not be registered.")
        
            elif command[2].lower() == 'comp9003' and len(studentID) == 9:
                alreadyin = any(sid == command[1] for sid, course, tut in class9003)
                if alreadyin:
                    print(f"Student {command[1]} is already registered in COMP9003.")
                else:
                    if len(class9003) < num_9003:
                        class9003.append((command[1], command[2], tut_group))
                        print(f"Student {command[1]} has been registered in {command[2].upper()} (Tutorial {tut_group}).")
                    elif len(class9003) > num_9003:
                        print(f"Course COMP9003 is full. Student {command[1]} could not be registered.")

            elif command[2].lower() == 'comp9017' and len(studentID) == 9:
                alreadyin = any(sid == command[1] for sid, course, tut in class9017)
                if alreadyin:
                    print(f"Student {command[1]} is already registered in COMP9017.")
                else:
                    if len(class9017) < num_9017:
                        class9017.append((command[1], command[2], tut_group))
                        print(f"Student {command[1]} has been registered in {command[2].upper()} (Tutorial {tut_group}).")
                    elif len(class9017) > num_9017:
                        print(f"Course COMP9003 is full. Student {command[1]} could not be registered.")
            elif command[2].lower() != 'comp9001' or 'comp9003' or'comp9017':
                print("Course COMP9020 does not exist.")

    elif action == 'drop':
        if command[2].lower() == 'comp9001':
            # find student in 9001
            student_found = False
            for i, (sid, course, tut) in enumerate(class9001):
                if sid == command[1]:
                    class9001.pop(i)
                    print(f"Student {command[1]} has been dropped from COMP9001.")
                    student_found = True
                    break
            if not student_found:
                print(f"Student {command[1]} is not registered in COMP9001.")

        elif command[2].lower() == 'comp9003':
            # find student in 9003
            student_found = False
            for i, (sid, course, tut) in enumerate(class9003):
                if sid == command[1]:
                    class9003.pop(i)
                    print(f"Student {command[1]} has been dropped from COMP9003.")
                    student_found = True
                    break
            if not student_found:
                print(f"Student {command[1]} is not registered in COMP9003.")

        elif command[2].lower() == 'comp9017':
            # fing student in 9017
            student_found = False
            for i, (sid, course, tut) in enumerate(class9017):
                if sid == command[1]:
                    class9017.pop(i)
                    print(f"Student {command[1]} has been dropped from COMP9017.")
                    student_found = True
                    break
            if not student_found:
                print(f"Student {command[1]} is not registered in COMP9017.")
        elif command[2].lower() not in ['comp9001', 'comp9003', 'comp9017']:
            print(f"Course {command[2]} does not exist.")

    elif action == 'schedule':
        targetid = command[1]
        enrolled = []

        for id, course, tut in class9001:
            if id == targetid:
                enrolled.append(f"{course.upper()} (Tutorial {tut})")
        for id, course, tut in class9003:
            if id == targetid:
                enrolled.append(f"{course.upper()} (Tutorial {tut})")
        for id, course, tut in class9017:
            if id == targetid:
                enrolled.append(f"{course.upper()} (Tutorial {tut})")
        if enrolled:
            print(f"Student {targetid} is enrolled in: {enrolled}")
        else:
            print("No courses registered.")


    elif action == 'courseinfo':
        if command[1] == 'COMP9001':
            capacity = num_9001
            enrolled = [{'id': sid, 'tutorial_group': int(tut)} for sid, _, tut in class9001]
        elif command[1] == 'COMP9003':
            capacity = num_9003
            enrolled = [{'id': sid, 'tutorial_group': int(tut)} for sid, _, tut in class9003]
        elif command[1] == 'COMP9017':
            capacity = num_9017
            enrolled = [{'id': sid, 'tutorial_group': int(tut)} for sid, _, tut in class9017]
        print(f"Course: {command[1]}")
        print(f"Capacity: {capacity}")
        print(f"Enrolled: {enrolled}")

    elif action == 'listcourses':
        print(f"COMP9001: {len(class9001)}/{num_9001}")
        print(f"COMP9003: {len(class9003)}/{num_9003}")
        print(f"COMP9017: {len(class9017)}/{num_9017}")

    elif action == 'help':
        print("Supported commands:\n REGISTER <student_id> <course>\n DROP <student_id> <course>\n SCHEDULE <student_id>\n COURSEINFO <course>\n LISTCOURSES\n HELP\n EXIT")
    elif action == 'exit':
        print("Goodbye!") 
        exit()
    else:
        print("Unknown command. Type HELP for commands.")