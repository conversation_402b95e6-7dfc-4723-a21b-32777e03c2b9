
--Write a SQL query to give the student IDs and names of all
--students who have enrolled in only one unit_of_study, and order the result by student
--ID. Note that, a student can enrol in the same unit_of_study multiple times, which is still
--counted as one unit_of_study 


--我的方法：
SELECT studId, name
from student natural join transcript
group by studid, name
having count(distinct uoscode) = 1
order by studid;

--教学方法：
SELECT studId, name
FROM Student NATURAL JOIN (
SELECT DISTINCT studId, uoSCode
FROM Transcript ) AS T
GROUP BY studId, name
HAVING count(*) = 1
ORDER BY studId;
