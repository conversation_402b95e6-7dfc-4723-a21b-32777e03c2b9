DROP TABLE IF EXISTS flight CASCADE;
DROP TABLE IF EXISTS plane CASCADE;

create table plane(plane_id VARCHAR(8) PRIMARY KEY,
					category VARCHAR(10) NOT NULL CHECK (category IN ('jet', 'turboprop')),
					capacity INTEGER NOT NULL CHECK (capacity > 0)
);

create table flight(flight_id INTEGER PRIMARY KEY,
					plane_id VARCHAR(8) REFERENCES plane(plane_id),
					departs DATE NOT NULL,
					origin VARCHAR(3) NOT NULL,
					destination VARCHAR(3) NOT NULL
					

);

ALTER TABLE flight
	ADD CONSTRAINT one_flight_per_day
	UNIQUE (plane_id, departs);

INSERT INTO plane (plane_id, category, capacity) VALUES
('A380', 'jet', 100),
('A320', 'turboprop', 200);

INSERT INTO flight (flight_id, plane_id, departs, origin, destination) VALUES
('8264', 'A380', '2025-09-01', 'SYD', 'MEL'),
('4733', 'A320', '2025-09-02', 'MEL', 'SYD');

SELECT * FROM plane;
SELECT * FROM flight;



	