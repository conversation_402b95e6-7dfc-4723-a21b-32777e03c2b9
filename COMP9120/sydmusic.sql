CREATE TABLE account(
    account_id BIGSERIAL PRIMARY KEY,
    login_name VA<PERSON>HAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    mobile VARCHAR(20) NOT NULL,
    CHECK (length(trim(full_name)) > 0)
);
CREATE TABLE artist(
    artist_id BIGINT PRIMARY KEY,
    <PERSON>OREI<PERSON><PERSON> KEY (artist_id) REFERENCES account(account_id) ON DELETE CASCADE
);
CREATE TABLE customer(
    customer_id BIGINT PRIMARY KEY,
    FOREIG<PERSON> KEY (customer_id) REFERENCES account(account_id) ON DELETE CASCADE,
    date_of_birth DATE NOT NULL,
    age INT NOT NULL CHECK (age >= 0),
    CHECK (date_of_birth <= CURRENT_DATE)
    -- We store both age and date_of_birth as required by the specification. 
    -- In practice, age can drift over time; production systems would compute it from date_of_birth (e.g., via application logic or triggers). 
    -- For this assignment, we enforce basic constraints (non-negative age, non-future DOB) and keep them consistent at the application level.
);
CREATE TABLE staff(
    staff_id BIGINT PRIMARY KEY,
    address VARCHAR(255) NOT NULL,
    compensation NUMERIC(9,2) NOT NULL CHECK (compensation > 0 AND compensation <= 200000),
    FOREIGN KEY (staff_id) REFERENCES account(account_id) ON DELETE CASCADE
);
CREATE TABLE track(
    track_id BIGSERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    duration_sec INT NOT NULL CHECK (duration_sec > 0),
    avg_rating NUMERIC(2,1) CHECK (avg_rating >= 0 AND avg_rating <= 5)
);
CREATE TABLE genre(
    genre_id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL
);

CREATE TABLE track_genre(
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    genre_id INT NOT NULL REFERENCES genre(genre_id) ON DELETE RESTRICT,
    PRIMARY KEY(track_id, genre_id)
);    
CREATE TABLE album(
    album_id BIGSERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    release_date DATE NOT NULL CHECK (release_date <= CURRENT_DATE)
);
CREATE TABLE album_track(
    album_id BIGINT NOT NULL REFERENCES album(album_id) ON DELETE CASCADE,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE RESTRICT,
    track_no   INT NOT NULL CHECK (track_no > 0),
    is_most_popular BOOLEAN DEFAULT FALSE,
    PRIMARY KEY(album_id, track_id)
);
-- Asseraition to ensure every album has at least one track
CREATE ASSERTION non_empty_album_tracks
    CHECK (
        NOT EXISTS (
            SELECT 1 FROM album a 
            WHERE NOT EXISTS (
                SELECT 1 FROM album_track at 
                WHERE at.album_id = a.album_id
            )
        )
    );
-- Tigger to ensure every album has at least one track
CREATE TRIGGER enforce_album_not_empty
    AFTER INSERT OR DELETE ON album_track
    FOR EACH ROW
    EXECUTE FUNCTION (check_album_not_empty());

-- Need a function to check album not empty    TBD...
-- 
CREATE TABLE role(
    role_id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL
)


CREATE TABLE track_artist(
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    artist_id BIGINT NOT NULL REFERENCES artist(artist_id) ON DELETE CASCADE,
    role_id  INT NOT NULL REFERENCES role(role_id) ON DELETE RESTRICT,
    PRIMARY KEY(track_id, artist_id)
)
-- Asseraition to ensure every track has at least one artist
CREATE ASSERTION non_empty_track_artist
    CHECK (
        NOT EXISTS (
            SELECT 1 FROM track t
            WHERE NOT EXISTS (
                SELECT 1 FROM track_artist ta
                WHERE ta.track_id = t.track_id
            )
        )
    );
-- In the ERD, Playlist is modeled as a weak entity dependent on Customer.
-- In the relational schema, however, we use a surrogate key playlist_id for simplicity, 
-- while enforcing uniqueness on (customer_id, name) to ensure no two playlists under the same customer share the same name.
-- We also include created_at and updated_at timestamps as a reasonable extension to track playlist lifecycle, 
-- noting that updated_at would require triggers or application logic to stay consistent in practice.
CREATE TABLE playlist(
    playlist_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    customer_id BIGINT NOT NULL REFERENCES customer(customer_id) ON DELETE CASCADE,
    UNIQUE (customer_id, name),
    CHECK (length(trim(name)) > 0)
);
--Although users often perceive playlists as simply a "set of songs," the specification clearly states that playlists must preserve the order preferred by the user.
--Therefore, in our relational schema we design playlist_track with (playlist_id, position) as the primary key to emphasize ordering as the fundamental property,
--while enforcing UNIQUE (playlist_id, track_id) to avoid duplicate entries. T
CREATE TABLE playlist_track(
    playlist_id BIGINT NOT NULL REFERENCES playlist(playlist_id) ON DELETE CASCADE,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE RESTRICT,
    position INT NOT NULL CHECK (position>0),
    PRIMARY KEY(playlist_id, position),
    UNIQUE(playlist_id, track_id)
);
CREATE TABLE customer_track_listen(
    customer_id BIGINT NOT NULL REFERENCES customer(customer_id) ON DELETE CASCADE,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    play_count INT NOT NULL CHECK (play_count >= 0),
    last_played_at TIMESTAMPTZ,
    PRIMARY KEY(customer_id, track_id)
);
CREATE TABLE review(
    review_id BIGSERIAL PRIMARY KEY,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    content TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    customer_id BIGINT NOT NULL REFERENCES customer(customer_id) ON DELETE CASCADE,
    UNIQUE(customer_id, track_id)
)
CREATE TABLE review_removal(
    removal_id BIGSERIAL PRIMARY KEY,
    review_id BIGINT NOT NULL REFERENCES review(review_id) ON DELETE CASCADE,
    staff_id BIGINT NOT NULL REFERENCES staff(staff_id) ON DELETE RESTRICT,
    removed_reason TEXT NOT NULL,
    removed_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(review_id)
)




