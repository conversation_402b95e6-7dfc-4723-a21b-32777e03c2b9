/*
==============================================================================
Assignment Notes: Business Rules Beyond DDL
==============================================================================

Some business rules in the specification cannot be fully enforced by standard 
DDL constraints (PRIMARY KEY, FOREIGN KEY, UNIQUE, CHECK). They are documented 
here for completeness:

1. Every album must contain at least one track.
   - In the SQL standard, this could be expressed with CREATE ASSERTION.
   - PostgreSQL does not support ASSERTION.
   - In practice, this rule would require a trigger or application logic.
   - For this assignment: documented here but not enforced in DDL.

2. Every track must have at least one associated artist.
   - Same limitation as above: requires ASSERTION or triggers in practice.
   - Not implementable in PostgreSQL DDL, documented instead.

3. Customer age vs. date_of_birth.
   - Both fields are stored, as explicitly required by the specification.
   - This introduces redundancy: in practice, age should be derived from DOB.
   - For this assignment, both are included to comply with requirements.

------------------------------------------------------------------------------
Note:
These comments acknowledge semantic integrity constraints and design choices 
that exceed PostgreSQL's DDL capabilities. They are included to demonstrate 
awareness of best practices and limitations.
==============================================================================
*/

CREATE TABLE account(
    account_id BIGSERIAL PRIMARY KEY,
    login_name VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    mobile VARCHAR(20) NOT NULL,
    CHECK (length(trim(full_name)) > 0)
);
CREATE TABLE artist(
    artist_id BIGINT PRIMARY KEY,
    FOREIGN KEY (artist_id) REFERENCES account(account_id) ON DELETE CASCADE
);
CREATE TABLE customer(
    customer_id BIGINT PRIMARY KEY,
    FOREIGN KEY (customer_id) REFERENCES account(account_id) ON DELETE CASCADE,
    date_of_birth DATE NOT NULL,
    age INT NOT NULL CHECK (age >= 0),
    CHECK (date_of_birth <= CURRENT_DATE)
);
CREATE TABLE staff(
    staff_id BIGINT PRIMARY KEY,
    address VARCHAR(255) NOT NULL,
    compensation NUMERIC(9,2) NOT NULL CHECK (compensation > 0 AND compensation <= 200000),
    FOREIGN KEY (staff_id) REFERENCES account(account_id) ON DELETE CASCADE
);
CREATE TABLE track(
    track_id BIGSERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    duration_sec INT NOT NULL CHECK (duration_sec > 0),
    avg_rating NUMERIC(2,1) CHECK (avg_rating >= 1 AND avg_rating <= 5)
);
CREATE TABLE genre(
    genre_id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL
);
CREATE TABLE track_genre(
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    genre_id INT NOT NULL REFERENCES genre(genre_id) ON DELETE RESTRICT,
    PRIMARY KEY(track_id, genre_id)
);    
CREATE TABLE album(
    album_id BIGSERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    release_date DATE NOT NULL CHECK (release_date <= CURRENT_DATE)
);
CREATE TABLE album_track(
    album_id BIGINT NOT NULL REFERENCES album(album_id) ON DELETE CASCADE,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE RESTRICT,
    track_no   INT NOT NULL CHECK (track_no > 0),
    is_most_popular BOOLEAN DEFAULT FALSE,
    PRIMARY KEY(album_id, track_id)
);

CREATE TABLE track_artist(
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    artist_id BIGINT NOT NULL REFERENCES artist(artist_id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL,
    PRIMARY KEY(track_id, artist_id, role)
);

-- In the ERD, Playlist is modeled as a weak entity dependent on Customer.
-- In the relational schema, however, we use a surrogate key playlist_id for simplicity, 
-- while enforcing uniqueness on (customer_id, name) to ensure no two playlists under the same customer share the same name.
CREATE TABLE playlist(
    playlist_id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    customer_id BIGINT NOT NULL REFERENCES customer(customer_id) ON DELETE CASCADE,
    UNIQUE (customer_id, name),
    CHECK (length(trim(name)) > 0)
);
--Although users often perceive playlists as simply a "set of songs," the specification clearly states that playlists must preserve the order preferred by the user.
--Therefore, in our relational schema we design playlist_track with (playlist_id, position) as the primary key to emphasize ordering as the fundamental property,
--while enforcing UNIQUE (playlist_id, track_id) to avoid duplicate entries. T
CREATE TABLE playlist_track(
    playlist_id BIGINT NOT NULL REFERENCES playlist(playlist_id) ON DELETE CASCADE,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE RESTRICT,
    position INT NOT NULL CHECK (position>0),
    PRIMARY KEY(playlist_id, position),
    UNIQUE(playlist_id, track_id)
);
CREATE TABLE customer_track_listen(
    customer_id BIGINT NOT NULL REFERENCES customer(customer_id) ON DELETE CASCADE,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    play_count INT NOT NULL CHECK (play_count >= 0),
    last_played_at TIMESTAMPTZ,
    PRIMARY KEY(customer_id, track_id)
);
CREATE TABLE review(
    review_id BIGSERIAL PRIMARY KEY,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    content TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    track_id BIGINT NOT NULL REFERENCES track(track_id) ON DELETE CASCADE,
    customer_id BIGINT NOT NULL REFERENCES customer(customer_id) ON DELETE CASCADE,
    UNIQUE(customer_id, track_id)
);
CREATE TABLE review_removal(
    removal_id BIGSERIAL PRIMARY KEY,
    review_id BIGINT NOT NULL REFERENCES review(review_id) ON DELETE CASCADE,
    staff_id BIGINT NOT NULL REFERENCES staff(staff_id) ON DELETE RESTRICT,
    removed_reason TEXT NOT NULL,
    removed_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(review_id)
);


BEGIN;

-- ========== Account and Subtypes ==========
INSERT INTO account(login_name, password_hash, full_name, email, mobile) VALUES ('kl4love1985', '$2b$12$LQv3c1yqBwlVHpaPb5eIXOQ9d1P2HZnorgch7bNCqsl0C9FEJNdOu', 'Kendrick Lamar', '<EMAIL>', '*********');
INSERT INTO artist(artist_id) VALUES (1);
INSERT INTO account(login_name, password_hash, full_name, email, mobile) VALUES ('musiclover92', '$2b$12$Np8vQ2rL9fK3mX7eH4sO.uY6zB1cJ5nW8pT9qR2vS4dF7aE3mL0kG', 'Sarah Johnson', '<EMAIL>', '***********');
INSERT INTO customer(customer_id, date_of_birth, age) VALUES (2, '1992-08-15', 31);
INSERT INTO account(login_name, password_hash, full_name, email, mobile) VALUES ('reviewer_01', '$2b$12$Tr7yV0wK8iP6qA1hL9uR.xB5cF4hM0qZ3sW6vU9yX8gI1dH6pO4nJ', 'Content Reviewer', '<EMAIL>', '**********');
INSERT INTO staff(staff_id, address, compensation) VALUES (3, '123 Admin St, Sydney, NSW', 85000.00);
INSERT INTO account(login_name, password_hash, full_name, email, mobile) VALUES ('jj1979', '$2b$12$Xy9zW1vK3mN4oP5qR6sT.uY7zB8cJ9nW0pQ1rS2tU3vW4xY5zA6bC', 'Jay Chou', '<EMAIL>', '***********');
INSERT INTO artist(artist_id) VALUES (4);
INSERT INTO account(login_name, password_hash, full_name, email, mobile) VALUES ('kfc44@po', '$2b$12$A1b2C3d4E5 f6G7h8I9j0K1l2M3n4O5p6Q7r8S9t0U1v2W3x4Y5z6A7b8C9d0E1f2G3h4I5j', 'Khalid Fong', '**********');
INSERT INTO artist(artist_id) VALUES (5);
-- ========== Track and Genre ==========
INSERT INTO genre(name) VALUES ('Hip Hop');
INSERT INTO genre(name) VALUES ('Jazz');
INSERT INTO genre(name) VALUES ('Rock');
INSERT INTO genre(name) VALUES ('Ballad');
-- 2 Hip Hop
INSERT INTO track(title, duration_sec, avg_rating) VALUES ('NOT LIKE US', 274, 4.6);
INSERT INTO track_genre(track_id, genre_id) VALUES (1, 1);
INSERT INTO track(title, duration_sec, avg_rating) VALUES ('Gods Plan', 198, 4.4);
INSERT INTO track_genre(track_id, genre_id) VALUES (2, 1);
-- 2 Ballad
INSERT INTO track(title, duration_sec, avg_rating) VALUES ('晴天', 269, 4.8);
INSERT INTO track_genre(track_id, genre_id) VALUES (3, 4);
INSERT INTO track(title, duration_sec, avg_rating) VALUES ('Love Song', 245, 4.3);
INSERT INTO track_genre(track_id, genre_id) VALUES (4, 4);
-- 1 Rock
INSERT INTO track(title, duration_sec, avg_rating) VALUES ('Bohemian Rhapsody', 355, 4.9);
INSERT INTO track_genre(track_id, genre_id) VALUES (5, 3);
-- 1 Jazz
INSERT INTO track(title, duration_sec, avg_rating) VALUES ('If It Aint You', 187, 4.2);
INSERT INTO track_genre(track_id, genre_id) VALUES (6, 2);

-- ========== Album and AlbumTrack ==========
INSERT INTO album(title, release_date) VALUES ('Yeh Hui-Mei', '2003-07-31');
INSERT INTO album_track(album_id, track_id, track_no, is_most_popular) VALUES (1, 3, 1, TRUE);
-- ========== Track-Artist ==========
INSERT INTO track_artist(track_id, artist_id, role) VALUES (3, 4, 'Singer');
INSERT INTO track_artist(track_id, artist_id, role) VALUES (3, 4, 'Lyricist');
-- ========== Playlist and PlaylistTrack ==========
INSERT INTO playlist(name, customer_id) VALUES ('My Hip Hop Hits', 2);
INSERT INTO playlist_track(playlist_id, track_id, position) VALUES (1, 1, 1);
-- ========== Customer Listening ==========
INSERT INTO customer_track_listen(customer_id, track_id, play_count, last_played_at) VALUES (2, 1, 5, '2024-10-01 14:30:00+10');
-- ========== Review and ReviewRemoval ==========
INSERT INTO review (rating, content, track_id, customer_id) VALUES (5, 'Amazing song!', 4, 2);
INSERT INTO review_removal (review_id, staff_id, removed_reason) VALUES (1, 3, 'Spam content');

COMMIT;
-- End of sydmusic.sql