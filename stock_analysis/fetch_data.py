import yfinance as yf

def fetch_data(stock_pitch: str, start="2024-01-01", end=None):

    print(f"📡 We are getting {stock_pitch} data from Yahoo Finance from 2024 till now...")
    raw_data = yf.download(stock_pitch, start=start, end=end, auto_adjust=True, progress=True)

    if raw_data.empty:
        print("Oops, seems no data for this stock. Please try a valid one.")
        return None

    raw_data.columns = raw_data.columns.get_level_values(0) #we want the closing price for the stock pitch, so we keep level_value(0)

    raw_data = raw_data.reset_index()[['Date', 'Close']] #for clearly illustration, we only keep date and close price for the annalysis

    filename = f"data/{stock_pitch}.csv"
    raw_data.to_csv(filename, index=False)

    print(f"✅ Stock data fetched and stored in {filename} successfully.")
    return raw_data