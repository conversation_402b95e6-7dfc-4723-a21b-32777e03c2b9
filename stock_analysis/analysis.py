import numpy as np

def analyze_returns(raw_data):
    raw_data['Daily Return'] = raw_data['Close'].pct_change() #we want to see the daily return

    avg_return = np.mean(raw_data['Daily Return']) * 100
    volatility = np.std(raw_data['Daily Return']) * 100
    max_return = np.max(raw_data['Daily Return']) * 100
    min_return = np.min(raw_data['Daily Return']) * 100

    print(f"Average Daily Return: {avg_return:.2f}%")
    print(f"Volatility(Risk Level): {volatility:.2f}%")
    print(f"Max Daily Return: {max_return:.2f}%")
    print(f"Max Daily Loss: {min_return:.2f}%")

    f = open("result.txt", "w")
    f.write("Average Daily Return: " + str(avg_return) + "%\n")
    f.write("Volatility(Risk Level): " + str(volatility) + "%\n")
    f.write("Max Daily Return: " + str(max_return) + "%\n")
    f.write("Max Daily Loss: " + str(min_return) + "%\n")
    f.close()

    return raw_data